package com.sinoyd.common.preview;

import com.aspose.pdf.Document;
import com.sinoyd.common.vo.DocumentPreviewVO;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;

/**
 * PDF文件预览
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2024/12/17
 **/
public class PDFDocumentPreviewer implements IDocumentPreviewer {

    @Override
    public void previewAsPDF(String rootPath, DocumentPreviewVO vo, HttpServletResponse response) {
        verifyPDFLicense();
        Document pdfDocument = null;
        Document copyDocument = null;
        String tempPath = rootPath + File.separator + "临时" + vo.getSourceFilePath();
        try {
            pdfDocument = new Document(rootPath + File.separator + vo.getSourceFilePath());
            copyDocument = new Document();
            for (int i = 1; i <= pdfDocument.getPages().size(); i++) {
                copyDocument.getPages().add(pdfDocument.getPages().get_Item(i));
            }
            if (!vo.getIsAddWaterMark()) {
                copyDocument.getInfo().setTitle(vo.getSourceFileName());
            }
            //保存复制的pdf文件
            copyDocument.save(tempPath);
            vo.setPreviewFileFullPath(tempPath);
            vo.setPreviewFileName(copyDocument.getFileName());
            postProcessing(vo, response);
        } catch (Exception e) {
            throw new RuntimeException("PDF文件预览失败");
        } finally {
            if (pdfDocument != null) {
                pdfDocument.close();
            }
            if (copyDocument != null) {
                pdfDocument.close();
            }
        }
    }

    @Override
    public void previewAsHtml(ByteArrayOutputStream os, HttpServletResponse response) {

    }

}
