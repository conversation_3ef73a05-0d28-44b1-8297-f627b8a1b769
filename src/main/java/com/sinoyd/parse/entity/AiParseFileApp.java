package com.sinoyd.parse.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;

import javax.persistence.*;

import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;

import java.util.Date;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.validation.constraints.NotNull;

/**
 * AI仪器解析应用实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@MappedSuperclass
@ApiModel(description = "AiParseFileApp")
@Data
@EntityListeners(AuditingEntityListener.class)
public class AiParseFileApp implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public AiParseFileApp() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.NewID();

    /**
     * 仪器id(AI仪器解析配置id)
     */
    @Column(length = 50)
    @ApiModelProperty("仪器id(AI仪器解析配置id)")
    @Length(max = 50, message = "仪器id长度不能超过50")
    private String instrumentId;

    /**
     * 仪器解析名称
     */
    @Column(length = 250)
    @ApiModelProperty("仪器解析名称")
    @Length(max = 250, message = "仪器解析名称长度不能超过250")
    private String instrumentName;

    /**
     * 解析方式（枚举管理：{@link com.sinoyd.parse.enums.EnumParseType}）
     */
    @Column
    @ApiModelProperty("解析方式（枚举管理：EnumParseType: 4.图像识别 5.文本提取）")
    @NotNull(message = "解析方式不能为空")
    private String parseType;

    /**
     * 解析状态（枚举管理：{@link com.sinoyd.parse.enums.EnumAIParseStatus}）
     */
    @Column
    @ApiModelProperty("解析状态（枚举管理：EnumAIParseStatus: 1.未解析 2.解析成功 3.解析失败）")
    @NotNull(message = "解析状态不能为空")
    private String parseStatus;

    /**
     * 组织机构Id
     */
    @Column(length = 50)
    @ApiModelProperty("组织机构Id")
    @Length(max = 50, message = "组织机构Id长度不能超过50")
    private String orgId;

    /**
     * 是否删除
     */
    @ApiModelProperty("是否删除")
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column(length = 50)
    @CreatedBy
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    @ApiModelProperty("创建时间")
    private Date createDate;

    /**
     * 更新人
     */
    @Column(length = 50)
    @LastModifiedBy
    @ApiModelProperty("更新人")
    private String modifier;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @ApiModelProperty("更新时间")
    private Date modifyDate;
}
