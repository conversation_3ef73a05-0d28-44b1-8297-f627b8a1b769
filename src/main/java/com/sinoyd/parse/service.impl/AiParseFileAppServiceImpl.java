package com.sinoyd.parse.service.impl;

import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.parse.dto.DtoParseDocument;
import com.sinoyd.parse.enums.EnumAIParseStatus;
import com.sinoyd.parse.enums.EnumDocType;
import com.sinoyd.parse.enums.EnumParseType;
import com.sinoyd.parse.repository.AiInstrumentConfigRepository;
import com.sinoyd.parse.repository.AiParseFileAppRepository;
import com.sinoyd.parse.repository.ParseDocumentRepository;
import com.sinoyd.parse.service.AiParseFileAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * AI仪器解析应用操作接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Slf4j
@Service
public class AiParseFileAppServiceImpl extends BaseJpaServiceImpl<DtoAiParseFileApp, String, AiParseFileAppRepository> implements AiParseFileAppService {

    private AiInstrumentConfigRepository aiInstrumentConfigRepository;

    private ParseDocumentRepository parseDocumentRepository;

    @Override
    public void findByPage(PageBean<DtoAiParseFileApp> pb, BaseCriteria aiParseFileAppCriteria) {
        pb.setEntityName("DtoAiParseFileApp a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, aiParseFileAppCriteria);

        // 设置解析方式名称和解析状态名称
        List<DtoAiParseFileApp> appList = pb.getData();
        loadTransientFields(appList);
    }

    @Override
    @Transactional
    public DtoAiParseFileApp save(DtoAiParseFileApp entity) {
        //设置解析状态为未解析
        entity.setParseStatus(EnumAIParseStatus.UN_PARS.getValue());
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoAiParseFileApp update(DtoAiParseFileApp entity) {
        return super.update(entity);
    }

    @Override
    public DtoAiParseFileApp findOne(String key) {
        DtoAiParseFileApp fileApp = super.findOne(key);
        loadTransientFields(Collections.singletonList(fileApp));
        return fileApp;
    }

    @Override
    public CompletableFuture<Void> batchParseStream(List<String> appIds, java.util.function.Consumer<String> onData) {
        return CompletableFuture.runAsync(() -> {
            if (appIds == null || appIds.isEmpty()) {
                return;
            }
            // 固定3线程并发执行
            java.util.concurrent.ExecutorService executor = java.util.concurrent.Executors.newFixedThreadPool(3);
            java.util.List<CompletableFuture<Void>> tasks = new java.util.ArrayList<>();

            for (String appId : appIds) {
                CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                    try {
                        // 1. 开始
                        sendStep(onData, appId, 1, "开始");
                        // 2. 预处理（示例逻辑，可替换为OCR/文本处理）
//                        DtoAiParseFileApp app = repository.findOne(appId);
//                        String step2Msg = (app != null && com.sinoyd.parse.enums.EnumParseType.IMAGE_RECOGNITION.getValue()
//                                .equals(String.valueOf(app.getParseType()))) ? "图像识别完成" : "文本提取完成";
                        sendStep(onData, appId, 2, "预处理完成");
                        // 3. AI解析（占位，可对接流式AI接口并逐行 onData.accept(json)）
                        sendStep(onData, appId, 3, "AI解析完成");
                        // 4. 结束
                        sendStep(onData, appId, 4, "结束");
                    } catch (Exception ex) {
                        sendStep(onData, appId, -1, "解析失败: " + ex.getMessage());
                    }
                }, executor);
                tasks.add(task);
            }

            try {
                CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();
            } finally {
                executor.shutdown();
            }
        });
    }

    @Override
    public DtoAiParseFileApp findAttachPath(String id) {
        return repository.findOne(id);
    }


    /**
     * 加载冗余字段
     *
     * @param data 应用数据
     */
    private void loadTransientFields(Collection<DtoAiParseFileApp> data) {
        //查询附件数据
        List<String> appIds = data.stream().map(DtoAiParseFileApp::getId).collect(Collectors.toList());
        List<DtoParseDocument> documentList = parseDocumentRepository.findByObjectIdInAndDocTypeId(appIds, EnumDocType.AI_PARSE_APP_FILE.getDocTypeId());
        //按照ObjectId分组附件数据
        Map<String, List<DtoParseDocument>> documentMap = documentList.stream().collect(Collectors.groupingBy(DtoParseDocument::getObjectId));
        //相关枚举Map
        Map<String, String> parseTypeMap = EnumParseType.getMapData();
        Map<String, String> parseStatusMap = EnumAIParseStatus.getMapData();

        for (DtoAiParseFileApp app : data) {
            //获取应用下的最新的附件
            List<DtoParseDocument> documents = documentMap.getOrDefault(app.getId(), null);
            if (documents != null && !documents.isEmpty()) {
                Optional<DtoParseDocument> docOp = documents.stream().max(Comparator.comparing(DtoParseDocument::getCreateDate));
                docOp.ifPresent(p -> {
                    app.setDocumentId(p.getId());
                    app.setDocumentPath(p.getPath());
                });

            }
            // 设置解析方式名称
            if (app.getParseType() != null) {
                String parseTypeName = parseTypeMap.getOrDefault(app.getParseType(), "");
                app.setParseTypeName(parseTypeName);
            }

            // 设置解析状态名称
            if (app.getParseStatus() != null) {
                String parseStatusName = parseStatusMap.getOrDefault(app.getParseStatus(), "");
                app.setParseStatusName(parseStatusName);
            }
        }
    }

    private void sendStep(java.util.function.Consumer<String> onData, String appId, int step, String data) {
        String safe = data == null ? "" : data.replace("\\", "\\\\").replace("\"", "\\\"");
        String payload = String.format("{\"id\":\"%s\",\"step\":%d,\"data\":\"%s\"}", appId, step, safe);
        onData.accept(payload);
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Autowired
    public void setAiInstrumentConfigRepository(AiInstrumentConfigRepository aiInstrumentConfigRepository) {
        this.aiInstrumentConfigRepository = aiInstrumentConfigRepository;
    }

    @Autowired
    public void setParseDocumentRepository(ParseDocumentRepository parseDocumentRepository) {
        this.parseDocumentRepository = parseDocumentRepository;
    }
}
