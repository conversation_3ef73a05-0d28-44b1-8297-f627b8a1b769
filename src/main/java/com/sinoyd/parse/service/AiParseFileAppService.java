package com.sinoyd.parse.service;

import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.frame.service.IBaseJpaService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * AI仪器解析应用操作接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
public interface AiParseFileAppService extends IBaseJpaService<DtoAiParseFileApp, String> {

    /**
     * 批量AI解析（SSE流式回调）
     *
     * @param appIds 解析应用ID集合
     * @param onData 流式数据回调（JSON字符串）。数据格式参考 doc/exampleCode/SSEStreamResponse.json
     * @return void 无返回，数据通过回调逐步返回
     */
    CompletableFuture<Void> batchParseStream(List<String> appIds, Consumer<String> onData);

    /**
     * 获取附件上传路径数据
     *
     * @param id 解析应用id
     * @return 解析应用数据
     */
    DtoAiParseFileApp findAttachPath(String id);
}
