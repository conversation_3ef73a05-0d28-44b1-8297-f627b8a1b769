# AI仪器解析数据库设计文档

## 1. 数据库设计规范

### 1.1 命名规范
- 数据库表名使用全小写，以`tb_parse`开头
- 字段名使用小驼峰命名方式，不使用下划线分隔
- 主键统一使用`id`字段，类型为`VARCHAR(50)`
- 所有表必须包含标准审计字段

### 1.2 标准字段
每个表必须包含以下标准字段：
```sql
id               VARCHAR(50)  NOT NULL COMMENT '主键',
orgId            VARCHAR(50)  NOT NULL COMMENT '所属机构ID',
domainId         VARCHAR(50)  NOT NULL COMMENT '所属实验室ID',
creator          VARCHAR(50)  NOT NULL COMMENT '创建人',
createDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
modifier         VARCHAR(50)  NOT NULL COMMENT '更新人',
modifyDate       DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
```

## 2. AI仪器解析配置相关表设计

### 2.1 仪器解析配置表（tb_parse_AiInstrumentConfig）

#### 2.1.1 表结构
```sql
DROP TABLE IF EXISTS tb_parse_AiInstrumentConfig;
CREATE TABLE tb_parse_AiInstrumentConfig
(
    id             VARCHAR(50)  NOT NULL COMMENT '主键',
    orgId          VARCHAR(50)  NOT NULL COMMENT '所属机构ID',
    instrumentType VARCHAR(20)  NOT NULL COMMENT '仪器类型：1-现场仪器，2-实验室仪器',
    instrumentName VARCHAR(200) NOT NULL COMMENT '仪器名称',
    instrumentCode VARCHAR(100) NULL COMMENT '仪器编号',
    parseFolder    VARCHAR(500) NULL COMMENT '解析文件目录',
    belongUnit     VARCHAR(200) NULL COMMENT '所属单位',
    promptText     TEXT NULL COMMENT '提示词内容',
    isEnabled      TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    isDeleted      TINYINT(1)   NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    creator        VARCHAR(50)  NOT NULL COMMENT '创建人',
    createDate     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier       VARCHAR(50)  NOT NULL COMMENT '更新人',
    modifyDate     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT='AI仪器解析配置表';
```
## 3. AI仪器解析模块表

### 3.1 AI解析模块应用表
#### 3.1.1 表结构
```sql
-- ---------------------------------------------------------------
-- 创建AI仪器解析应用表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_PARSE_AiParseFileApp;
CREATE TABLE TB_PARSE_AiParseFileApp(
    id VARCHAR(50) NOT NULL   COMMENT '主键' ,
    instrumentId VARCHAR(50) NOT NULL   COMMENT '仪器id(AI仪器解析配置id)' ,
    instrumentName VARCHAR(250) NOT NULL   COMMENT '仪器解析名称' ,
    parseType INT(11) NOT NULL   COMMENT '解析方式（枚举管理：EnumAIParseType: 1.图像识别）' ,
    parseStatus INT(11) NOT NULL   COMMENT '解析状态（枚举管理）' ,
    isDeleted BIT(1) NOT NULL  DEFAULT b'0' COMMENT '假删 标识' ,
    orgId VARCHAR(50) NOT NULL   COMMENT '所属机构ID' ,
    creator VARCHAR(50) NOT NULL   COMMENT '创建人' ,
    createDate DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    modifier VARCHAR(50) NOT NULL   COMMENT '更新人' ,
    modifyDate DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
) COMMENT = 'AI仪器解析应用表';
```
### 3.2 解析应用日志表（已经存在）
 - 对应当前系统中的实体为 com.sinoyd.parse.entity.Log

### 3.3 解析过程日志表（已经存在）
 - 对应当前系统中的实体为 com.sinoyd.parse.entity.AppFlowData

### 3.4 解析结果表（已经存在）
 - 对应当前系统中的实体为 com.sinoyd.parse.entity.Datas

### 3.5 仪器解析文档关联表
 - 用于AI仪器解析应用上传的解析文件关联
 - 用于AI仪器解析过程日志产生的结果文件关联
#### 3.5.1 表结构
```sql
-- ---------------------------------------------------------------
-- 创建仪器解析文件管理表映射
-- ---------------------------------------------------------------
DROP TABLE IF EXISTS TB_PARSE_ParseDocument;
CREATE TABLE TB_PARSE_ParseDocument
(
    id             VARCHAR(50) NOT NULL COMMENT '主键',
    objectId       VARCHAR(50) NOT NULL COMMENT '对象id',
    folderName     VARCHAR(255) COMMENT '文件夹名称',
    filename       VARCHAR(255) COMMENT '文件名称',
    physicalName   VARCHAR(255) COMMENT '物理文件名称',
    path           VARCHAR(500) COMMENT '文件路径',
    docTypeId      VARCHAR(50) NOT NULL COMMENT '文件类型',
    docTypeName    VARCHAR(255) COMMENT '文件类型名称',
    docSize        INT(11) NOT NULL   COMMENT '文件大小',
    docSuffix      VARCHAR(50) COMMENT '文件后缀',
    orderNum       INT(11) NOT NULL   COMMENT '排序值',
    remark         VARCHAR(1000) COMMENT '备注',
    uploadPersonId VARCHAR(50) NOT NULL COMMENT '上传人id',
    uploadPerson   VARCHAR(50) NOT NULL COMMENT '上传人名称',
    isDeleted      BIT(1)      NOT NULL DEFAULT b'0' COMMENT '假删 标识',
    orgId          VARCHAR(50) NOT NULL COMMENT '所属机构ID',
    creator        VARCHAR(50) NOT NULL COMMENT '创建人',
    createDate     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modifier       VARCHAR(50) NOT NULL COMMENT '更新人',
    modifyDate     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '仪器解析文件管理表';

```





## 4. 数据字典

### 4.1 仪器类型枚举（新增）
 - 对应当前系统中的枚举为 com.sinoyd.parse.enums.EnumInstrumentType
| 值 | 说明 |
|----|------|
| 1 | 现场仪器 |
| 2 | 实验室仪器 |

### 4.2 解析方式枚举（已有枚举下新增枚举值）
 - 对应当前系统中的枚举为 com.sinoyd.parse.enums.EnumParseType

| 值 | 说明 |
|---|------|
| 4 | 图像识别 |
| 5 | 文本提取 |


### 4.3 解析状态枚举（已创建）
 - 对应当前系统中的枚举为 com.sinoyd.parse.enums.EnumParseLogStatus
| 值 | 说明 |
|---|------|
| 1 | 待解析 |
| 2 | 解析成功 |
| 3 | 解析失败 |
